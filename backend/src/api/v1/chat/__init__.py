from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel
import uuid
import logging
from datetime import datetime
import json
# Use the existing agent from test.py
from core.security import get_tenant_info
from models.user import UserTenantDB
from core.smart_logger import log_chat, log_error, log_success
from core.pagination import PaginatedResponse
from bson import ObjectId
# Import the existing agent from test.py
from api.services.test import extract_and_save_user_info, get_session_history, create_dynamic_agent_executor
from langchain_core.runnables.history import RunnableWithMessageHistory

logger = logging.getLogger(__name__)

router = APIRouter(tags=["Chat"])


class ChatRequest(BaseModel):
    message: str


class ToolUsed(BaseModel):
    name: str
    description: str
    input: dict = {}
    output: str = ""

async def insert_user_message(current_user: UserTenantDB, session_id: str, user_message: str):
    """Insert user message and return document ID for later update"""
    try:
        # Get the MongoDB database from the user's connection
        db = current_user.async_db[current_user.tenant_database_name]
        chat_exchanges_collection = db["chat_exchanges"]

        timestamp = datetime.now()

        # Create document with user message only
        chat_exchange_doc = {
            "session_id": session_id,
            "request": {
                "user_message": user_message,
                "timestamp": timestamp
            },
            "response": None,  # Will be updated later
            "created_at": timestamp
        }

        # Insert the document and return the ID
        result = await chat_exchanges_collection.insert_one(chat_exchange_doc)
        log_chat(f"Inserted user message for session {session_id}")
        return result.inserted_id

    except Exception as e:
        log_error(f"Failed to insert user message: {e}")
        return None


async def update_with_ai_response(current_user: UserTenantDB, document_id:ObjectId, ai_response: str, tools_used: list[ToolUsed]):
    """Update the document with AI response and tools"""
    try:
        # Get the MongoDB database from the user's connection
        db = current_user.async_db[current_user.tenant_database_name]
        chat_exchanges_collection = db["chat_exchanges"]

        timestamp = datetime.now()

        # Update document with AI response
        update_data = {
            "response": {
                "ai_reply": ai_response,
                "tools_used": [
                    {
                        "name": tool.name,
                        "description": tool.description,
                        "input": tool.input,
                        "output": tool.output
                    } for tool in tools_used
                ],
                "timestamp": timestamp
            }
        }

        await chat_exchanges_collection.update_one(
            {"_id": document_id},
            {"$set": update_data}
        )
        log_chat(f"Updated response with {len(tools_used)} tools for document {document_id}")

    except Exception as e:
        log_error(f"Failed to update AI response: {e}")


async def get_chat_exchanges(current_user: UserTenantDB, session_id: str, page: int = 1, limit: int = 20) -> dict:
    """Retrieve chat exchanges for a session with pagination"""
    try:
        # Get the MongoDB database from the user's connection
        db = current_user.async_db[current_user.tenant_database_name]
        chat_exchanges_collection = db["chat_exchanges"]

        # Calculate skip value for pagination
        skip = (page - 1) * limit

        # Get total count
        total_count = await chat_exchanges_collection.count_documents({"session_id": session_id})

        # Find exchanges for this session with pagination, sorted by timestamp (newest first)
        exchanges = []
        async for doc in chat_exchanges_collection.find({"session_id": session_id}).sort("created_at", -1).skip(skip).limit(limit):
            exchanges.append(doc)

        # Reverse to show oldest first in the current page
        exchanges.reverse()

        return {
            "exchanges": exchanges,
            "total_count": total_count,
            "page": page,
            "limit": limit,
            "total_pages": (total_count + limit - 1) // limit,
            "has_next": page * limit < total_count,
            "has_prev": page > 1
        }

    except Exception as e:
        log_error(f"Failed to retrieve chat exchanges: {e}")
        return {
            "exchanges": [],
            "total_count": 0,
            "page": page,
            "limit": limit,
            "total_pages": 0,
            "has_next": False,
            "has_prev": False
        }


async def clear_chat_data(current_user: UserTenantDB, session_id: str):
    """Clear chat data from both collections"""
    try:
        # Get the MongoDB database from the user's connection
        db = current_user.async_db[current_user.tenant_database_name]

        # Clear from our custom chat exchanges collection
        chat_exchanges_collection = db["chat_exchanges"]
        result1 = await chat_exchanges_collection.delete_many({"session_id": session_id})

        # Clear from LangChain's chat history collection
        chat_history = get_session_history(current_user)
        chat_history.clear()

        log_chat(f"Cleared {result1.deleted_count} chat exchanges and LangChain history for session {session_id}")

    except Exception as e:
        log_error(f"Failed to clear chat data: {e}")


class ChatResponse(BaseModel):
    response: str
    thread_id: str
    user_id: str
    tools_used: list[ToolUsed] = []
    message_id: str  # Unique ID for this chat exchange


class ChatMessage(BaseModel):
    type: str  # "human" or "ai"
    content: str
    timestamp: str = None
    tools_used: list[ToolUsed] = []


class ChatHistoryResponse(BaseModel):
    user_id: str
    session_id: str
    messages: list[ChatMessage]
    total_messages: int
    page: int
    limit: int
    total_pages: int
    has_next: bool
    has_prev: bool


@router.post("/chat", response_model=ChatResponse)
async def chat(
    chat_request: ChatRequest,
    current_user: UserTenantDB = Depends(get_tenant_info)
):
    """
    Chat endpoint using simple dynamic LangMem tools
    """
    try:
        # Use user ID as session ID
        user_id = str(current_user.user.id)
        session_id = f"user_{user_id}"

        log_chat(f"Chat request from user {current_user.user.username} (ID: {user_id}): {chat_request.message[:50]}...")

        # Step 1: Insert user message immediately
        document_id = await insert_user_message(current_user, session_id, chat_request.message)

        # Step 2: Create dynamic agent with tenant-specific tools and history
        dynamic_agent_executor = create_dynamic_agent_executor(current_user)

        tenant_agent = RunnableWithMessageHistory(
            dynamic_agent_executor,
            lambda sid: get_session_history(current_user),
            input_messages_key="input",
            history_messages_key="chat_history",
        )

        # Step 3: Process with agent
        try:
            agent_response = await tenant_agent.ainvoke(
                {"input": chat_request.message},
                config={"configurable": {"session_id": session_id}}
            )
            print(f"Agent response: {agent_response}")

            log_chat(f"Chat response generated for user {current_user.user.username}")

            # Process agent response - the agent returns output in 'output' key
            response_text = agent_response.get("output", "")

        except Exception as agent_error:
            log_error(f"Agent execution failed: {agent_error}")
            # Provide a helpful fallback response
            response_text = "I apologize, but I encountered an issue processing your request. Please try again or contact support if the problem persists."
            agent_response = {"output": response_text, "intermediate_steps": []}

        # Extract tool calls from intermediate steps
        intermediate_steps = agent_response.get("intermediate_steps", [])
        tools_used = []

        for step in intermediate_steps:
            # Each step is a tuple: (AgentAction, tool_output)
            if len(step) == 2:
                agent_action, tool_output = step

                # Extract tool information from AgentAction
                tool_name = getattr(agent_action, 'tool', 'unknown_tool')
                tool_input = getattr(agent_action, 'tool_input', {})

                # Convert tool_input to dict if it's a string
                if isinstance(tool_input, str):
                    try:
                        import json
                        tool_input = json.loads(tool_input)
                    except (json.JSONDecodeError, ValueError):
                        tool_input = {"input": tool_input}

                # Create ToolUsed object
                tool_used = ToolUsed(
                    name=tool_name,
                    description=f"Execute {tool_name} tool",
                    input=tool_input,
                    output=str(tool_output) if tool_output else "Tool executed successfully"
                )
                tools_used.append(tool_used)

        # Step 4: Update document with AI response and tools
        if document_id:
            await update_with_ai_response(current_user, document_id, response_text, tools_used)

        chat_response = ChatResponse(
            response=response_text,
            thread_id=session_id,  # Use session_id as thread_id
            user_id=user_id,
            message_id=str(uuid.uuid4()),
            tools_used=tools_used
        )

        return chat_response

    except Exception as e:
        log_error(f"Agent Chat failed for user {current_user.user.username} {e}")
        raise HTTPException(
            status_code=500,
            detail="Failed to process chat request"
        )
    finally:
        # Auto-extract and save user information using dynamic memory tools
        auto_save_result = extract_and_save_user_info(chat_request.message, current_user)
        if auto_save_result:
            logger.info(f"Auto-saved: {auto_save_result}")



@router.get("/chat/history")
async def get_chat_history(
    page: int = 1,
    limit: int = 20,
    current_user: UserTenantDB = Depends(get_tenant_info)
):
    """
    Get chat history for the current user
    """
    try:
        # Use user ID as session ID
        user_id = str(current_user.user.id)
        session_id = f"user_{user_id}"

        log_chat(f"Fetching chat history for user {current_user.user.username} (ID: {user_id})")

        # Get chat exchanges from our custom storage with pagination
        result = await get_chat_exchanges(current_user, session_id, page, limit)
        chat_exchanges = result["exchanges"]
        total_count = result["total_count"]

        # Convert exchanges to message format
        messages = []
        for exchange in chat_exchanges:
            # Add user message
            user_msg = ChatMessage(
                type="human",
                content=exchange["request"]["user_message"],
                timestamp=str(exchange["request"]["timestamp"]),
                tools_used=[]
            )
            messages.append(user_msg)

            # Add AI response with tools
            tools_used = []
            if exchange["response"] and exchange["response"]["tools_used"]:
                for tool_data in exchange["response"]["tools_used"]:
                    tool_used = ToolUsed(
                        name=tool_data.get('name', 'unknown_tool'),
                        description=tool_data.get('description', ''),
                        input=tool_data.get('input', {}),
                        output=tool_data.get('output', '')
                    )
                    tools_used.append(tool_used)

            ai_msg = ChatMessage(
                type="ai",
                content=exchange["response"]["ai_reply"] if exchange["response"] else "",
                timestamp=str(exchange["response"]["timestamp"]) if exchange["response"] else str(exchange["request"]["timestamp"]),
                tools_used=tools_used
            )
            messages.append(ai_msg)

        # Return paginated response
        return PaginatedResponse.create(
            data=messages,
            page=page,
            limit=limit,
            total_items=total_count
        )

    except Exception as e:
        log_error(f"Failed to fetch chat history for user {current_user.user.username} ,error {e}")
        raise HTTPException(
            status_code=500,
            detail="Failed to fetch chat history"
        )


@router.delete("/chat/history")
async def clear_chat_history(
    current_user: UserTenantDB = Depends(get_tenant_info)
):
    """
    Clear chat history for the current user
    """
    try:
        # Use user ID as session ID
        user_id = str(current_user.user.id)
        session_id = f"user_{user_id}"

        log_chat(f"Clearing chat history for user {current_user.user.username} (ID: {user_id})")

        # Clear chat data from both collections
        await clear_chat_data(current_user, session_id)

        log_success(f"Chat history cleared for user {current_user.user.username}")

        return {
            "status": "success",
            "message": f"Chat history cleared for user {current_user.user.username}",
            "user_id": user_id,
            "session_id": session_id
        }

    except Exception as e:
        log_error(f"Failed to clear chat history for user {current_user.user.username}", e)
        raise HTTPException(
            status_code=500,
            detail="Failed to clear chat history"
        )