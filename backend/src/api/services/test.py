from api.services.retreival import get_today_date, qna_search, product_search
from api.services.tools import get_all_tools, extract_and_save_user_info, get_user_info, create_dynamic_memory_tools
import os
from dotenv import load_dotenv
from models.user import UserTenantDB
from typing import Dict, List, Any, Optional
import json

from langchain.agents import create_tool_calling_agent, AgentExecutor
from langchain.chat_models import init_chat_model
from langchain_core.runnables.history import RunnableWithMessageHistory
from langchain_core.prompts import Chat<PERSON>romptTemplate
from langchain_mongodb.chat_message_histories import MongoDBChatMessageHistory
from langchain_core.callbacks import BaseCallbackHandler
from langchain_core.outputs import LL<PERSON><PERSON>ult
from langchain_core.messages import BaseMessage
from langchain_core.agents import AgentAction, AgentFinish
load_dotenv(override=True)

# Verify OpenAI API key is loaded
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
if not OPENAI_API_KEY:
    raise ValueError("OPENAI_API_KEY not found in environment variables")

# Combine retrieval tools with LangMem memory tools
tools = [get_today_date, qna_search, product_search] + get_all_tools()

# Simple model setup
model = init_chat_model(
    model="gpt-4o-mini",
    temperature=0.1,
    api_key=OPENAI_API_KEY,
)


# Simple prompt with LangMem
prompt = ChatPromptTemplate.from_messages([
    ("system", """You are a helpful assistant with LangMem memory capabilities.

TOOLS:
- product_search: Find courses/products
- qna_search: Technical support
- get_today_date: Current date/time
- save_user_info: Save user contact details
- get_user_profile: View saved user info
- book_service: Book services (auto-uses saved info)
- search_bookings: Find previous bookings
- delete_booking: Delete/cancel a booking by ID
- cancel_booking: Cancel a booking by ID (alias for delete)
- user_memory/search_user_memory: LangMem tools for user data
- booking_memory/search_booking_memory: LangMem tools for bookings

USAGE:
- Product questions → product_search
- Tech issues → qna_search
- Booking → book_service (auto-gets saved user info)
- Save contact info → save_user_info
- View profile → get_user_profile
- Booking history → search_bookings
- Delete/cancel booking → delete_booking or cancel_booking (provide booking ID)

When user provides name/email/phone, automatically save it.
For bookings, get saved user info first, ask for missing details if needed.
For deletions, extract booking ID from user request."""),
    ("placeholder", "{chat_history}"),
    ("human", "{input}"),
    ("placeholder", "{agent_scratchpad}"),
])

# Tenant-aware MongoDB session history
def get_session_history(current_user: UserTenantDB) -> MongoDBChatMessageHistory:
    """Get MongoDB chat history with tenant-specific database and user-specific collection"""



    return MongoDBChatMessageHistory(
        # client=current_user.db,
        connection_string=os.getenv("MONGO_URI"),
        database_name=current_user.tenant_database_name,
        collection_name="chat_history",
        session_id=f"user_{current_user.user.id}"
    )

# Dynamic agent creation with tenant-specific tools
def create_dynamic_agent_executor(current_user: UserTenantDB):
    """Create agent executor with tenant-specific tools and memory"""
    try:
        # Get dynamic memory tools for this user
        memory_tools = create_dynamic_memory_tools(current_user)

        # Combine all tools: retrieval + basic tools + dynamic memory tools
        all_tools = [get_today_date, qna_search, product_search] + get_all_tools() + memory_tools

        # Create agent with dynamic tools
        dynamic_agent = create_tool_calling_agent(model, all_tools, prompt)
        # Enable return_intermediate_steps to capture tool calls
        dynamic_agent_executor = AgentExecutor(
            agent=dynamic_agent,
            tools=all_tools,
            verbose=True,
            return_intermediate_steps=True
        )

        return dynamic_agent_executor

    except Exception as e:
        print(f"Error creating dynamic agent: {e}")
        # Fallback to basic agent without memory tools
        basic_tools = [get_today_date, qna_search, product_search] + get_all_tools()
        fallback_agent = create_tool_calling_agent(model, basic_tools, prompt)
        return AgentExecutor(
            agent=fallback_agent,
            tools=basic_tools,
            verbose=True,
            return_intermediate_steps=True
        )

# Basic agent setup (for backward compatibility)
agent = create_tool_calling_agent(model, tools, prompt)
agent_executor = AgentExecutor(agent=agent, tools=tools, verbose=True)

