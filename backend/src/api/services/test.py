from api.services.retreival import get_today_date, qna_search, product_search
from api.services.tools import get_all_tools, extract_and_save_user_info, get_user_info, create_dynamic_memory_tools
import os
from dotenv import load_dotenv
from models.user import UserTenantDB
from typing import Dict, List, Any, Optional
import json

from langchain.agents import create_tool_calling_agent, AgentExecutor
from langchain.chat_models import init_chat_model
from langchain_core.runnables.history import RunnableWithMessageHistory
from langchain_core.prompts import Chat<PERSON>romptTemplate
from langchain_mongodb.chat_message_histories import MongoDBChatMessageHistory
from langchain_core.callbacks import BaseCallbackHandler
from langchain_core.outputs import LL<PERSON><PERSON>ult
from langchain_core.messages import BaseMessage
from langchain_core.agents import AgentAction, AgentFinish
load_dotenv(override=True)

# Verify OpenAI API key is loaded
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
if not OPENAI_API_KEY:
    raise ValueError("OPENAI_API_KEY not found in environment variables")

# Combine retrieval tools with LangMem memory tools
tools = [get_today_date, qna_search, product_search] + get_all_tools()

# Simple model setup
model = init_chat_model(
    model="gpt-4o-mini",
    temperature=0.1,
    api_key=OPENAI_API_KEY,
)


# EduMind AI Educational Assistant Prompt
prompt = ChatPromptTemplate.from_messages([
    ("system", """
You are EduMind AI, an intelligent educational assistant for an educational institution in Nepal. You help students and parents with course information, admissions, academic guidance, and educational services.

IDENTITY & CONTEXT:
- You are EduMind AI, representing an educational institution in Nepal
- You understand and respond to queries in English, Nepali, and Hindi
- You specialize in educational guidance, course information, and academic support
- You help with IELTS preparation, SEE Bridge courses, BBS, BBA, CSIT, and other academic programs

LANGUAGE UNDERSTANDING:
- "yaha k k padna painxa" (Nepali) = "What can I study here?" → Use product_search to find available courses
- "ke ke course haru xa" (Nepali) = "What courses are available?" → Use product_search
- "admission kaise le" (Hindi/Nepali mix) = "How to take admission?" → Use qna_search for admission process
- Always interpret educational queries and search for relevant information

AVAILABLE TOOLS:
- product_search: Find and identify specific courses, programs, and educational services (USE ONLY for discovering what courses exist)
- qna_search: Get detailed information about pricing, fees, admissions, procedures, requirements, schedules, and general inquiries
- get_today_date: Current date/time information
- save_user_info: Save student/parent contact details
- get_user_profile: View saved user information
- book_service: Book educational services, consultations, or courses
- search_bookings: Find previous bookings and enrollments
- delete_booking: Cancel a booking or enrollment by ID
- cancel_booking: Cancel a booking by ID (alias for delete)
- user_memory/search_user_memory: Remember and retrieve user-specific information
- booking_memory/search_booking_memory: Track booking and enrollment history

SEARCH STRATEGY:
1. **For general inquiries (fees, pricing, schedules, requirements, procedures)** → Use qna_search
2. **For identifying/discovering what courses exist** → Use product_search
3. **For specific course details after identifying the course** → Use qna_search

RESPONSE GUIDELINES:
1. ALWAYS search for relevant information using tools before responding
2. For fee/pricing queries → Use qna_search("course_name fee" or "pricing")
3. For "what courses are available" → Use product_search("subject area")
4. For admission procedures → Use qna_search("admission process")
5. For course schedules/timing → Use qna_search("course_name schedule")
6. Provide helpful, accurate educational guidance
7. Be encouraging and supportive about educational goals
8. Offer to help with next steps (enrollment, consultation, etc.)
9. Automatically save contact information when provided
10. Respond in the same language as the query when possible

USAGE PATTERNS:
- "What courses do you have?" → product_search("courses" or subject area)
- "NASU ko fee kati?" → qna_search("NASU fee" or "NASU pricing")
- "Admission kaise le?" → qna_search("admission process")
- "IELTS class ko time?" → qna_search("IELTS schedule" or "IELTS timing")
- Course discovery → product_search("subject area")
- Course details/info → qna_search("course_name details")
- Booking courses → book_service (auto-uses saved info)
- Save student info → save_user_info
- View profile → get_user_profile
- Enrollment history → search_bookings

Remember: You are an educational counselor helping students achieve their academic goals. Always be helpful, informative, and encouraging."""),
    ("placeholder", "{chat_history}"),
    ("human", "{input}"),
    ("placeholder", "{agent_scratchpad}"),
])

# Tenant-aware MongoDB session history
def get_session_history(current_user: UserTenantDB) -> MongoDBChatMessageHistory:
    """Get MongoDB chat history with tenant-specific database and user-specific collection"""



    return MongoDBChatMessageHistory(
        # client=current_user.db,
        connection_string=os.getenv("MONGO_URI"),
        database_name=current_user.tenant_database_name,
        collection_name="chat_history",
        session_id=f"user_{current_user.user.id}"
    )

# Dynamic agent creation with tenant-specific tools
def create_dynamic_agent_executor(current_user: UserTenantDB):
    """Create agent executor with tenant-specific tools and memory"""
    try:
        # Get dynamic memory tools for this user
        memory_tools = create_dynamic_memory_tools(current_user)

        # Combine all tools: retrieval + basic tools + dynamic memory tools
        all_tools = [get_today_date, qna_search, product_search] + get_all_tools() + memory_tools

        # Create agent with dynamic tools
        dynamic_agent = create_tool_calling_agent(model, all_tools, prompt)
        # Enable return_intermediate_steps to capture tool calls
        dynamic_agent_executor = AgentExecutor(
            agent=dynamic_agent,
            tools=all_tools,
            verbose=True,
            return_intermediate_steps=True
        )

        return dynamic_agent_executor

    except Exception as e:
        print(f"Error creating dynamic agent: {e}")
        # Fallback to basic agent without memory tools
        basic_tools = [get_today_date, qna_search, product_search] + get_all_tools()
        fallback_agent = create_tool_calling_agent(model, basic_tools, prompt)
        return AgentExecutor(
            agent=fallback_agent,
            tools=basic_tools,
            verbose=True,
            return_intermediate_steps=True
        )

# Basic agent setup (for backward compatibility)
agent = create_tool_calling_agent(model, tools, prompt)
agent_executor = AgentExecutor(agent=agent, tools=tools, verbose=True)

