"""
Dynamic LangMem Memory Tools with LLM-powered structured data extraction
Clean, flexible, and intelligent user info and booking management
"""

import json
from datetime import datetime
from typing import Dict, Optional, Any
from langchain_core.tools import tool
from langchain_core.prompts import ChatPromptTemplate
from langchain.chat_models import init_chat_model
from langmem import create_manage_memory_tool, create_search_memory_tool
from models.user import UserTenantDB
import os
from dotenv import load_dotenv
import pymongo
from typing import Optional

load_dotenv()

# Dynamic memory tools that use tenant-specific MongoDB collections
def create_dynamic_memory_tools(current_user: UserTenantDB):
    """Create dynamic memory tools using tenant-specific MongoDB collections"""
    try:
        # Get memory configuration from tenant database
        memory_config = current_user.db.settings.find_one({"name": "memory_config"})
        if not memory_config:
            # Create default memory config if not exists
            memory_config = {
                "name": "memory_config",
                "user_memory_collection": "user_memories",
                "namespace_template": ("memories", "user_{user_id}"),
                "embedding_model": "openai:text-embedding-3-small",
                "embedding_dims": 1536
            }
            current_user.db.settings.insert_one(memory_config)

        # Create namespace for this specific user
        user_namespace = ("memories", f"user_{current_user.user.id}")

        # Create memory management tool with tenant-specific configuration
        manage_tool = create_manage_memory_tool(
            namespace=user_namespace,
            instructions="""Proactively save important information about the user including:
            1. User preferences and settings
            2. Important personal information (name, contact details, etc.)
            3. Business context and requirements
            4. Previous conversation context that should be remembered
            5. User feedback and corrections""",
            name="manage_user_memory"
        )

        # Create memory search tool
        search_tool = create_search_memory_tool(
            namespace=user_namespace,
            instructions="Search through saved user information and conversation history to provide personalized responses.",
            name="search_user_memory"
        )

        return [manage_tool, search_tool]

    except Exception as e:
        print(f"Error creating dynamic memory tools: {e}")
        return []

# Memory tools are now created dynamically per user via create_dynamic_memory_tools()

# LLM for structured extraction
llm = init_chat_model(
    model="gpt-4o-mini",
    temperature=0.1,
    api_key=os.getenv("OPENAI_API_KEY"),
)

# LLM-powered extraction prompts
EXTRACT_USER_INFO_PROMPT = ChatPromptTemplate.from_messages([
    ("system", """Extract user information from the text. Return ONLY a JSON object with these fields:
    - name: full name (null if not found)
    - email: email address (null if not found)
    - phone: 10-digit phone number starting with 9 (null if not found or invalid)

    Example: {"name": "John Doe", "email": "<EMAIL>", "phone": "9841234567"}
    Return {"name": null, "email": null, "phone": null} if nothing found."""),
    ("human", "Text: {text}")
])

def extract_user_info_with_llm(text: str) -> Dict[str, Optional[str]]:
    """Use LLM to extract structured user info"""
    try:
        prompt = EXTRACT_USER_INFO_PROMPT.format(text=text)
        response = llm.invoke(prompt)

        # Parse JSON response
        info = json.loads(response.content)

        # Validate phone format
        if info.get("phone"):
            phone = str(info["phone"]).replace(" ", "").replace("-", "")
            if len(phone) != 10 or not phone.startswith('9') or not phone.isdigit():
                info["phone"] = None

        return info
    except:
        return {"name": None, "email": None, "phone": None}

def extract_and_save_user_info(text: str, current_user: UserTenantDB = None) -> str:
    """Auto-extract and save user info using dynamic memory tools"""
    if not current_user:
        return ""

    info = extract_user_info_with_llm(text)
    if not any(info.values()):
        return ""

    try:
        # Get dynamic memory tools for this user
        memory_tools = create_dynamic_memory_tools(current_user)
        if not memory_tools:
            return ""

        manage_tool = memory_tools[0]  # manage_user_memory
        saved = []

        for key, value in info.items():
            if value:
                manage_tool.invoke({"content": f"User {key}: {value}"})
                saved.append(f"{key}: {value}")

        return f"💾 Saved: {', '.join(saved)}" if saved else ""
    except Exception as e:
        print(f"Error saving user info: {e}")
        return ""

def get_user_info(current_user: UserTenantDB = None) -> Dict[str, Optional[str]]:
    """Get saved user info using dynamic memory search"""
    if not current_user:
        return {"name": None, "email": None, "phone": None}

    try:
        # Get dynamic memory tools for this user
        memory_tools = create_dynamic_memory_tools(current_user)
        if not memory_tools or len(memory_tools) < 2:
            return {"name": None, "email": None, "phone": None}

        search_tool = memory_tools[1]  # search_user_memory

        # Search for user information
        result = search_tool.invoke({"query": "user name email phone contact information"})

        if result:
            # Use LLM to extract structured data from search results
            extract_prompt = ChatPromptTemplate.from_messages([
                ("system", """Extract user information from the search results. Return ONLY a JSON object:
                {"name": "value or null", "email": "value or null", "phone": "value or null"}"""),
                ("human", "Search results: {results}")
            ])

            prompt = extract_prompt.format(results=result)
            response = llm.invoke(prompt)
            return json.loads(response.content)
    except Exception as e:
        print(f"Error getting user info: {e}")

    return {"name": None, "email": None, "phone": None}

# Old function removed - using the new create_dynamic_memory_tools(current_user) instead

@tool
def save_user_info(name: str = "", email: str = "", phone: str = "", user_id: str = "default") -> str:
    """Save user contact information dynamically"""
    # Get user-specific memory tools
    user_memory, _, _, _ = create_dynamic_memory_tools(user_id, "session")

    saved = []
    errors = []

    # Dynamic validation rules
    validation_rules = {
        "name": {"min_length": 2, "required": False},
        "email": {"pattern": ["@", "."], "required": False},
        "phone": {"length": 10, "start_with": "9", "required": False}
    }

    # Validate name
    if name:
        if len(name.strip()) >= validation_rules["name"]["min_length"]:
            user_memory.invoke({"content": f"User name: {name.strip()}"})
            saved.append("name")
        else:
            errors.append("name too short")

    # Validate email
    if email:
        if all(pattern in email for pattern in validation_rules["email"]["pattern"]):
            user_memory.invoke({"content": f"User email: {email.strip()}"})
            saved.append("email")
        else:
            errors.append("invalid email format")

    # Validate phone
    if phone:
        clean_phone = ''.join(c for c in phone if c.isdigit())
        rules = validation_rules["phone"]
        if len(clean_phone) == rules["length"] and clean_phone.startswith(rules["start_with"]):
            user_memory.invoke({"content": f"User phone: {clean_phone}"})
            saved.append("phone")
        else:
            errors.append(f"phone must be {rules['length']} digits starting with {rules['start_with']}")

    # Build response
    if not saved and not errors:
        return "❌ No information provided"

    response_parts = []
    if saved:
        response_parts.append(f"✅ Saved {', '.join(saved)}")
    if errors:
        response_parts.append(f"❌ Errors: {', '.join(errors)}")

    return " | ".join(response_parts)

@tool
def get_user_profile(user_id: str = "default") -> str:
    """Get user profile dynamically"""
    info = get_user_info_dynamic(user_id)

    if any(info.values()):
        profile = []
        for key, value in info.items():
            if value:
                profile.append(f"{key.title()}: {value}")
        return f"👤 Profile:\n" + "\n".join(f"• {p}" for p in profile)
    else:
        return "👤 No profile saved yet"

def get_user_info_dynamic(user_id: str) -> Dict[str, Optional[str]]:
    """Get saved user info dynamically"""
    try:
        _, search_user_memory, _, _ = create_dynamic_memory_tools(user_id, "session")

        # Search for user information
        result = search_user_memory.invoke({"query": "user name email phone contact information"})

        if result:
            # Use LLM to extract structured data from search results
            extract_prompt = ChatPromptTemplate.from_messages([
                ("system", """Extract user information from the search results. Return ONLY a JSON object:
                {"name": "value or null", "email": "value or null", "phone": "value or null"}"""),
                ("human", "Search results: {results}")
            ])

            prompt = extract_prompt.format(results=result)
            response = llm.invoke(prompt)
            return json.loads(response.content)
    except:
        pass

    return {"name": None, "email": None, "phone": None}

@tool
def book_service(service_name: str, user_name: str = "", user_email: str = "", user_phone: str = "") -> str:
    """Book a service with auto-retrieval of saved user info"""
    # Get saved info
    saved_info = get_user_info()

    # Use provided info or fall back to saved info
    user_name = user_name or saved_info.get("name", "")
    user_email = user_email or saved_info.get("email", "")
    user_phone = user_phone or saved_info.get("phone", "")

    # Check required fields
    missing = []
    if not user_name or len(user_name.strip()) < 2:
        missing.append("name")
    if not user_email or "@" not in user_email or "." not in user_email:
        missing.append("email")
    if not user_phone:
        missing.append("phone")
    else:
        # Clean and validate phone
        clean_phone = ''.join(c for c in user_phone if c.isdigit())
        if len(clean_phone) != 10 or not clean_phone.startswith('9'):
            missing.append("valid phone (10 digits starting with 9)")
        else:
            user_phone = clean_phone

    if missing:
        return f"❌ Missing: {', '.join(missing)}. Please provide to complete booking."

    # Create booking
    booking_id = f"BK{datetime.now().strftime('%Y%m%d%H%M%S')}"
    booking_info = {
        "booking_id": booking_id,
        "service": service_name,
        "customer": user_name,
        "email": user_email,
        "phone": user_phone,
        "date": datetime.now().strftime('%Y-%m-%d %H:%M')
    }

    # Note: Booking memory functionality moved to dynamic memory tools
    # This function now only returns confirmation without saving to memory

    return f"""✅ Booking Confirmed!

📋 ID: {booking_id}
🎯 Service: {service_name}
👤 Customer: {user_name}
📧 Email: {user_email}
📱 Phone: {user_phone}
📅 Date: {booking_info['date']}

Thank you!"""

@tool
def search_bookings(query: str = "") -> str:
    """Search bookings with LLM-powered formatting"""
    try:
        # Note: Booking search functionality moved to dynamic memory tools
        # This function now returns a placeholder message
        return "📋 Booking search functionality moved to dynamic memory tools. Please use the agent's memory search capabilities."
    except Exception as e:
        return f"❌ Search failed: {str(e)}"

@tool
def delete_booking(booking_id: str) -> str:
    """Delete/cancel a booking by booking ID"""
    try:
        # Create a deletion record (simple approach - just mark as deleted)
        deletion_info = {
            "action": "deleted",
            "booking_id": booking_id,
            "deleted_at": datetime.now().strftime('%Y-%m-%d %H:%M'),
            "status": "cancelled"
        }

        # Note: Booking cancellation memory moved to dynamic memory tools
        # This function now only returns confirmation without saving to memory

        return f"""✅ Booking Cancelled Successfully!

🗑️ Booking ID: {booking_id}
📅 Cancelled at: {deletion_info['deleted_at']}
✅ Status: Cancelled

Your booking has been cancelled and removed from active bookings."""

    except Exception as e:
        return f"❌ Cancellation failed: {str(e)}"

@tool
def cancel_booking(booking_id: str) -> str:
    """Cancel a booking by booking ID (alias for delete_booking)"""
    return delete_booking(booking_id)

# Export tools
def get_all_tools():
    """Return all available tools (memory tools are now created dynamically per user)"""
    return [
        save_user_info,
        get_user_profile,
        book_service,
        search_bookings,
        delete_booking,
        cancel_booking
        # Note: Memory tools (user_memory, search_user_memory, booking_memory, search_booking_memory)
        # are now created dynamically per user via create_dynamic_memory_tools(current_user)
    ]
