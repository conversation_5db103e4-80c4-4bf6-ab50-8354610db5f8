/**
 * Chat Service
 * Handles chat API communication and message management
 */

import { http } from './baseHttp';

// Types
export interface ToolUsed {
  name: string;
  description?: string;
  input?: any;
  output?: any;
}

export interface ChatMessage {
  id: string;
  message: string;
  response: string;
  timestamp: Date;
  thread_id: string;
  user_id: string;
  tools_used?: ToolUsed[];
}

export interface ChatRequest {
  message: string;
}

export interface ChatResponse {
  response: string;
  thread_id: string;
  user_id: string;
  message_id: string;
  tools_used?: ToolUsed[];
}

export interface ChatHistoryMessage {
  type: 'human' | 'ai';
  content: string;
  timestamp?: Date;
  tools_used?: ToolUsed[];
}

export interface PaginationMeta {
  page: number;
  limit: number;
  total_items: number;
  total_pages: number;
  has_next: boolean;
  has_prev: boolean;
}

export interface ChatHistoryResponse {
  data: ChatHistoryMessage[];
  meta: PaginationMeta;
}

export interface ClearHistoryResponse {
  success: boolean;
  message: string;
}

export interface StreamChunk {
  type: 'metadata' | 'chunk' | 'complete' | 'error';
  content?: string;
  index?: number;
  thread_id?: string;
  user_id?: string;
  message_id?: string;
  tools_used?: ToolUsed[];
  full_response?: string;
  error?: string;
}

/**
 * Chat Service Class
 */
class ChatService {
  private readonly CHAT_HISTORY_KEY = 'chat_history';

  /**
   * Send a chat message to the API
   */
  async sendMessage(message: string): Promise<ChatResponse> {
    try {
      const response = await http.post<ChatResponse>('/api/v1/chat', {
        message,
      });

      // Store message in local history
      this.addToHistory({
        id: response.data.message_id || Date.now().toString(),
        message,
        response: response.data.response,
        timestamp: new Date(),
        thread_id: response.data.thread_id,
        user_id: response.data.user_id,
        tools_used: response.data.tools_used || [],
      });

      return response.data;
    } catch (error: unknown) {
      const axiosError = error as { response?: { data?: { detail?: string } } };
      throw new Error(axiosError.response?.data?.detail || 'Failed to send message');
    }
  }

  /**
   * Send a message with streaming response
   */
  async sendMessageStream(
    message: string,
    onChunk: (chunk: StreamChunk) => void,
    onComplete: (response: ChatResponse) => void,
    onError: (error: string) => void
  ): Promise<void> {
    try {
      const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000';
      const response = await fetch(`${API_BASE_URL}/api/v1/stream`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
        },
        body: JSON.stringify({ message }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error('No response body reader available');
      }

      const decoder = new TextDecoder();
      let buffer = '';
      let metadata: any = null;
      let fullResponse = '';

      try {
        while (true) {
          const { done, value } = await reader.read();
          if (done) break;

          buffer += decoder.decode(value, { stream: true });
          const lines = buffer.split('\n');
          buffer = lines.pop() || '';

          for (const line of lines) {
            if (line.startsWith('data: ')) {
              try {
                const chunk: StreamChunk = JSON.parse(line.slice(6));

                if (chunk.type === 'metadata') {
                  metadata = chunk;
                } else if (chunk.type === 'chunk') {
                  fullResponse += chunk.content || '';
                  onChunk(chunk);
                } else if (chunk.type === 'complete') {
                  // Create final response object
                  const finalResponse: ChatResponse = {
                    response: chunk.full_response || fullResponse,
                    thread_id: metadata?.thread_id || '',
                    user_id: metadata?.user_id || '',
                    message_id: metadata?.message_id || Date.now().toString(),
                    tools_used: metadata?.tools_used || [],
                  };

                  // Store in local history
                  this.addToHistory({
                    id: finalResponse.message_id,
                    message,
                    response: finalResponse.response,
                    timestamp: new Date(),
                    thread_id: finalResponse.thread_id,
                    user_id: finalResponse.user_id,
                    tools_used: finalResponse.tools_used,
                  });

                  onComplete(finalResponse);
                } else if (chunk.type === 'error') {
                  onError(chunk.error || 'Unknown streaming error');
                }
              } catch (parseError) {
                console.warn('Failed to parse streaming chunk:', parseError);
              }
            }
          }
        }
      } finally {
        reader.releaseLock();
      }
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to send streaming message';
      onError(errorMessage);
    }
  }

  /**
   * Get chat history from local storage
   */
  getChatHistory(): ChatMessage[] {
    const historyStr = localStorage.getItem(this.CHAT_HISTORY_KEY);
    if (!historyStr) return [];

    try {
      const history = JSON.parse(historyStr);
      return history.map((msg: { id: string; message: string; response: string; timestamp: string; user_id?: string }) => ({
        ...msg,
        timestamp: new Date(msg.timestamp),
      }));
    } catch {
      return [];
    }
  }

  /**
   * Add message to chat history
   */
  private addToHistory(message: ChatMessage): void {
    const history = this.getChatHistory();
    history.push(message);

    // Keep only last 100 messages
    if (history.length > 100) {
      history.splice(0, history.length - 100);
    }

    localStorage.setItem(this.CHAT_HISTORY_KEY, JSON.stringify(history));
  }

  /**
   * Get chat history from server using MongoDBChatMessageHistory
   */
  async getServerChatHistory(page: number = 1, limit: number = 5): Promise<ChatHistoryResponse> {
    try {
      const params = new URLSearchParams();
      params.append('page', page.toString());
      params.append('limit', limit.toString());

      const response = await http.get<ChatHistoryResponse>(`/api/v1/chat/history?${params.toString()}`);
      return response.data;
    } catch (error: unknown) {
      const axiosError = error as { response?: { data?: { detail?: string } } };
      throw new Error(axiosError.response?.data?.detail || 'Failed to get chat history');
    }
  }

  /**
   * Clear chat history from both local storage and server
   */
  async clearHistory(): Promise<void> {
    try {
      // Clear server-side conversation memory
      await http.delete('/api/v1/chat/clear');

      // Clear local storage
      localStorage.removeItem(this.CHAT_HISTORY_KEY);
    } catch (error: unknown) {
      const axiosError = error as { response?: { data?: { detail?: string } } };
      console.error('Failed to clear conversation:', axiosError.response?.data?.detail || 'Unknown error');

      // Still clear local storage even if server call fails
      localStorage.removeItem(this.CHAT_HISTORY_KEY);

      throw new Error(axiosError.response?.data?.detail || 'Failed to clear conversation');
    }
  }

  /**
   * Clear server chat history using new API
   */
  async clearServerChatHistory(userId?: string): Promise<ClearHistoryResponse> {
    try {
      const params = new URLSearchParams();
      if (userId) params.append('user_id', userId);

      const response = await http.delete<ClearHistoryResponse>(`/api/v1/chat/history?${params.toString()}`);
      return response.data;
    } catch (error: unknown) {
      const axiosError = error as { response?: { data?: { detail?: string } } };
      throw new Error(axiosError.response?.data?.detail || 'Failed to clear chat history');
    }
  }

  /**
   * Clear all chat history for current user
   */
  async clearAllChatHistory(): Promise<ClearHistoryResponse> {
    try {
      const response = await http.post<ClearHistoryResponse>('/api/v1/clear-all');

      // Also clear local storage
      localStorage.removeItem(this.CHAT_HISTORY_KEY);

      return response.data;
    } catch (error: unknown) {
      const axiosError = error as { response?: { data?: { detail?: string } } };
      throw new Error(axiosError.response?.data?.detail || 'Failed to clear all chat history');
    }
  }

  /**
   * Get all chat sessions
   */
  async getChatSessions(): Promise<any> {
    try {
      const response = await http.get('/api/v1/sessions');
      return response.data;
    } catch (error: unknown) {
      const axiosError = error as { response?: { data?: { detail?: string } } };
      throw new Error(axiosError.response?.data?.detail || 'Failed to get chat sessions');
    }
  }

  /**
   * Check chat service health
   */
  async checkHealth(): Promise<boolean> {
    try {
      await http.get('/api/v1/chat/health');
      return true;
    } catch {
      return false;
    }
  }
}

// Export singleton instance
export const chatService = new ChatService();
export default chatService;
