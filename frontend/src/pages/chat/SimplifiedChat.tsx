import React, { useState, useRef, useEffect } from 'react';
import { <PERSON><PERSON>, User, Code2, Trash2, MessageSquare, RefreshCw, Home } from 'lucide-react';
import { useNavigate, useLocation } from 'react-router-dom';
import Button from '../../components/Button';
import ConfirmationModal from '../../components/ConfirmationModal';
import { useChat } from '../../hooks/useChat';
import ChatInput from './components/ChatInput';

const SimplifiedChat: React.FC = () => {
  const {
    messages,
    isLoading,
    error,
    sendMessage,
    clearHistory,
    refreshHistory,
    loadMoreHistory,
    hasMoreHistory,
    isLoadingMore,
    selectedMessage,
    setSelectedMessage,
    userTyping,
    setUserTyping,
    assistantTyping
  } = useChat();

  const navigate = useNavigate();
  const location = useLocation();
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const [showToolsPanel, setShowToolsPanel] = useState(false);
  const [pendingUserMessage, setPendingUserMessage] = useState<string | null>(null);
  const [showClearConfirm, setShowClearConfirm] = useState(false);
  const [isClearingHistory, setIsClearingHistory] = useState(false);

  // Helper function to format timestamp
  const formatTimestamp = (timestamp: Date | string) => {
    const date = timestamp instanceof Date ? timestamp : new Date(timestamp);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  // Get the selected message and check if it has tool calls
  const selectedMsg = messages.find(msg => msg.id === selectedMessage);
  const selectedHasToolCalls = selectedMsg?.tools_used && selectedMsg.tools_used.length > 0;

  // Auto-scroll to bottom when new messages arrive OR when typing states change
  useEffect(() => {
    if (!isLoadingMore) {
      messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages, isLoadingMore, userTyping, assistantTyping]);

  // Auto-show tools panel when a message with tool calls is selected
  useEffect(() => {
    if (selectedMessage && selectedHasToolCalls) {
      setShowToolsPanel(true);
    } else if (!selectedMessage) {
      setShowToolsPanel(false);
    }
  }, [selectedMessage, selectedHasToolCalls]);

  const handleSendMessage = async (message: string) => {
    try {
      // Show user message immediately
      setPendingUserMessage(message);
      await sendMessage(message);
      // Clear pending message when response is received
      setPendingUserMessage(null);
    } catch (error) {
      console.error('Failed to send message:', error);
      setPendingUserMessage(null);
    }
  };

  const handleClearHistory = () => {
    setShowClearConfirm(true);
  };

  const confirmClearHistory = async () => {
    setIsClearingHistory(true);
    try {
      await clearHistory(); // This calls the API to clear server history
      await refreshHistory(); // Refresh to get updated state from server
      setShowClearConfirm(false);
    } catch (error) {
      console.error('Failed to clear chat history:', error);
    } finally {
      setIsClearingHistory(false);
    }
  };

  return (
    <div className="h-screen flex bg-gray-50">
      {/* Simple Sidebar */}
      <div className="w-64 bg-gray-900 text-white flex flex-col">
        {/* Header */}
        <div className="p-4 border-b border-gray-700">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg flex items-center justify-center">
              <Bot className="h-5 w-5 text-white" />
            </div>
            <span className="font-semibold">EduMind AI</span>
          </div>
        </div>



        {/* Navigation */}
        <div className="flex-1 px-4">
          <div className="text-xs text-gray-400 mb-2">Navigation</div>
          <div className="space-y-1">
            <div
              onClick={() => navigate('/dashboard')}
              className={`px-3 py-2 rounded text-sm cursor-pointer hover:bg-gray-700 flex items-center ${
                location.pathname === '/dashboard' ? 'bg-gray-800 text-white' : 'text-gray-300'
              }`}
            >
              <Home className="h-4 w-4 mr-2" />
              Dashboard
            </div>
            <div
              onClick={() => navigate('/playground')}
              className={`px-3 py-2 rounded text-sm cursor-pointer hover:bg-gray-700 flex items-center ${
                location.pathname === '/playground' ? 'bg-gray-800 text-white' : 'text-gray-300'
              }`}
            >
              <MessageSquare className="h-4 w-4 mr-2" />
              Playground
            </div>
          </div>
        </div>


      </div>

      {/* Main Chat Area */}
      <div className="flex-1 flex flex-col">
        {/* Top Controls */}
        <div className="border-b border-gray-200 bg-white px-4 py-3">
          <div className="max-w-4xl mx-auto flex items-center justify-between">
            <h1 className="text-lg font-semibold text-gray-900">Playground</h1>
            <div className="flex items-center space-x-2">
              <Button
                onClick={refreshHistory}
                disabled={isLoading}
                variant="ghost"
                size="sm"
                className="text-gray-600 hover:text-gray-900"
              >
                <RefreshCw className="h-4 w-4 mr-1" />
                Refresh
              </Button>
              <Button
                onClick={handleClearHistory}
                disabled={messages.length === 0}
                variant="ghost"
                size="sm"
                className="text-red-600 hover:text-red-700"
              >
                <Trash2 className="h-4 w-4 mr-1" />
                Clear Chat
              </Button>
            </div>
          </div>
        </div>

        {/* Chat Messages */}
        <div className="flex-1 overflow-y-auto">
          <div className="max-w-4xl mx-auto px-4 py-8">
            {/* Load More Button */}
            {hasMoreHistory && (
              <div className="text-center mb-6">
                <Button
                  onClick={loadMoreHistory}
                  disabled={isLoadingMore}
                  variant="outline"
                  size="sm"
                >
                  {isLoadingMore ? 'Loading...' : 'Load more messages'}
                </Button>
              </div>
            )}

            {/* Welcome Message */}
            {messages.length === 0 && (
              <div className="text-center py-16">
                <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Bot className="h-8 w-8 text-white" />
                </div>
                <h2 className="text-2xl font-semibold text-gray-900 mb-2">Welcome to EduMind AI</h2>
                <p className="text-gray-600">How can I help you today?</p>
              </div>
            )}

            {/* Messages */}
            <div className="space-y-6">
              {messages.map((msg) => (
                <div key={msg.id}>
                  {/* User Message */}
                  <div className="flex justify-end mb-4">
                    <div className="flex items-start space-x-3 max-w-2xl">
                      <div className="bg-blue-600 text-white px-4 py-2 rounded-2xl rounded-br-md">
                        <p className="text-sm">{msg.message}</p>
                      </div>
                      <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center flex-shrink-0">
                        <User className="h-4 w-4 text-white" />
                      </div>
                    </div>
                  </div>

                  {/* AI Response */}
                  <div className="flex justify-start">
                    <div className="flex items-start space-x-3 max-w-2xl">
                      <div className="w-8 h-8 bg-gray-800 rounded-full flex items-center justify-center flex-shrink-0">
                        <Bot className="h-4 w-4 text-white" />
                      </div>
                      <div
                        className={`bg-white border px-4 py-3 rounded-2xl rounded-bl-md shadow-sm cursor-pointer transition-all ${
                          selectedMessage === msg.id
                            ? 'border-blue-400 shadow-md'
                            : msg.tools_used && msg.tools_used.length > 0
                            ? 'border-purple-200 hover:border-purple-300'
                            : 'border-gray-200 hover:border-gray-300'
                        }`}
                        onClick={() => setSelectedMessage(selectedMessage === msg.id ? null : msg.id)}
                      >
                        <div className="prose prose-sm max-w-none">
                          <p className="text-sm text-gray-900 whitespace-pre-wrap">{msg.response}</p>
                        </div>

                        {/* Tool indicator */}
                        {msg.tools_used && msg.tools_used.length > 0 && (
                          <div className="mt-2 pt-2 border-t border-gray-100">
                            <div className="flex items-center text-xs text-purple-600">
                              <Code2 className="h-3 w-3 mr-1" />
                              {msg.tools_used.length} tool{msg.tools_used.length > 1 ? 's' : ''} used
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              ))}

              {/* User Typing Animation - IN chat window */}
              {userTyping && (
                <div className="flex justify-end mb-4">
                  <div className="flex items-start space-x-3 max-w-2xl">
                    <div className="bg-blue-600 text-white px-4 py-2 rounded-2xl rounded-br-md opacity-70">
                      <div className="flex items-center space-x-2">
                        <div className="flex space-x-1">
                          <div className="w-1.5 h-1.5 bg-white rounded-full animate-bounce"></div>
                          <div className="w-1.5 h-1.5 bg-white rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                          <div className="w-1.5 h-1.5 bg-white rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                        </div>
                        <span className="text-xs opacity-80">typing...</span>
                      </div>
                    </div>
                    <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center flex-shrink-0 opacity-70">
                      <User className="h-4 w-4 text-white" />
                    </div>
                  </div>
                </div>
              )}



              {/* Assistant Typing Animation - appears as next message in the list */}
              {assistantTyping && (
                <div key="assistant-typing" className="flex justify-start">
                  <div className="flex items-start space-x-3 max-w-2xl">
                    <div className="w-8 h-8 bg-gray-800 rounded-full flex items-center justify-center flex-shrink-0">
                      <Bot className="h-4 w-4 text-white" />
                    </div>
                    <div className="bg-white border px-4 py-3 rounded-2xl rounded-bl-md shadow-sm">
                      <div className="flex items-center space-x-2">
                        <div className="flex space-x-1">
                          <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                          <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                          <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                        </div>
                        <span className="text-xs text-gray-500">AI is thinking...</span>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Error Message */}
            {error && (
              <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mt-4">
                <p className="text-sm">{error}</p>
              </div>
            )}

            <div ref={messagesEndRef} />
          </div>
        </div>

        {/* Chat Input */}
        <div className="border-t border-gray-200 bg-white p-4">
          <div className="max-w-4xl mx-auto">
            <ChatInput
              onSendMessage={handleSendMessage}
              isLoading={isLoading}
              placeholder="Message EduMind AI..."
              onTyping={setUserTyping}
            />
          </div>
        </div>
      </div>

      {/* Tools Panel - Side Drawer Overlay */}
      {showToolsPanel && selectedHasToolCalls && (
        <>
          {/* Backdrop */}
          <div
            className="fixed inset-0 bg-black bg-opacity-50 z-40"
            onClick={() => setShowToolsPanel(false)}
          />

          {/* Side Drawer */}
          <div className="fixed right-0 top-0 h-full w-96 bg-white shadow-xl z-50 overflow-y-auto">
            <div className="px-4 py-3 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <h3 className="font-medium text-gray-900">Tools Used</h3>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowToolsPanel(false)}
                  className="text-gray-500 hover:text-gray-700"
                >
                  ×
                </Button>
              </div>
            </div>

            <div className="p-4">
              {selectedMsg?.tools_used?.map((tool, index) => (
                <div key={index} className="mb-4 p-4 bg-gray-50 rounded-lg border">
                  <div className="flex items-center mb-2">
                    <Code2 className="h-4 w-4 text-purple-600 mr-2" />
                    <h4 className="font-medium text-sm text-gray-900">{tool.name}</h4>
                  </div>
                  {tool.description && (
                    <p className="text-xs text-gray-600 mb-3">{tool.description}</p>
                  )}

                  <div className="space-y-3">
                    {tool.input && (
                      <div>
                        <div className="text-xs font-medium text-gray-700 mb-1">INPUT:</div>
                        <pre className="text-xs bg-white p-2 rounded border overflow-x-auto whitespace-pre-wrap">
                          {typeof tool.input === 'string' ? tool.input : JSON.stringify(tool.input, null, 2)}
                        </pre>
                      </div>
                    )}

                    {tool.output && (
                      <div>
                        <div className="text-xs font-medium text-gray-700 mb-1">OUTPUT:</div>
                        <pre className="text-xs bg-white p-2 rounded border overflow-x-auto whitespace-pre-wrap">
                          {typeof tool.output === 'string' ? tool.output : JSON.stringify(tool.output, null, 2)}
                        </pre>
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </>
      )}

      {/* Clear History Confirmation Modal */}
      <ConfirmationModal
        isOpen={showClearConfirm}
        onClose={() => setShowClearConfirm(false)}
        onConfirm={confirmClearHistory}
        title="Clear Chat History"
        message="Are you sure you want to clear all chat history? This action cannot be undone and will permanently delete all your conversations."
        confirmText="Clear History"
        cancelText="Cancel"
        type="danger"
        isLoading={isClearingHistory}
      />
    </div>
  );
};

export default SimplifiedChat;
